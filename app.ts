import "source-map-support/register";
import * as cdk from "aws-cdk-lib";

import {
  createSynthesizer,
  SynthesizerSSMConfig,
} from "./synthesizer/create-synthesizer";
import { applyTagsToStacks, sanitizeTags } from "./utils/cdk-tagging";
import { getConfig, AppConfig } from "./utils/config-util";

import { CommonResourceStack } from "./stacks/common/common-resources-stack";

async function main() {
  const app = new cdk.App();
  const environment = app.node.tryGetContext("stage") || "intca1";
  const config: AppConfig = getConfig(environment);

  const synthesizerConfig: SynthesizerSSMConfig =
    config.defaultStackSynthesizer;
  const synthesizer = await createSynthesizer(synthesizerConfig);

  const stackConfigProps = config.stacks.CommonResourceStack;
  const commonResourceStack = new CommonResourceStack(
    app,
    stackConfigProps.stackName,
    {
      env: { account: config.account, region: config.region },
      config,
      stackConfigProps,
      synthesizer,
      terminationProtection: environment === "prodca1",
    }
  );

  const sanitizedTags = sanitizeTags(config.tags);
  applyTagsToStacks([commonResourceStack], sanitizedTags);

  app.synth();
}

main().catch((err) => {
  console.error("CDK App failed:", err);
  process.exit(1);
});
