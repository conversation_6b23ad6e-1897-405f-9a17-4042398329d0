import * as lambda from "aws-cdk-lib/aws-lambda";
import * as lambdaNodejs from "aws-cdk-lib/aws-lambda-nodejs";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as logs from "aws-cdk-lib/aws-logs";

import { Duration, RemovalPolicy, StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";
import { ISecurityGroup, IVpc } from "aws-cdk-lib/aws-ec2";

export interface SharedLambdaConstructProps extends StackProps {
  environment: string;
  vpc: IVpc;
  securityGroup: ISecurityGroup;
  functionName: string;
  logging: {
    retentionDays?: number;
  };
  dynatraceCredentialsSecretName: string
  memorySize?: number;
  timeout?: number;
  handlerFilePath: string;
  handlerFunctionName: string;
  description: string;
  envVariables?: Record<string, string>;
  region: string;
}

export class SharedLambdaConstruct extends Construct {
  public readonly function: lambdaNodejs.NodejsFunction;

  constructor(scope: Construct, id: string, props: SharedLambdaConstructProps) {
    super(scope, id);

    const envName = props.environment;
    const functionName = props.functionName +'-'+envName;

    const paramsAndSecrets = lambda.ParamsAndSecretsLayerVersion.fromVersion(
      lambda.ParamsAndSecretsVersions.V1_0_103,
      {
        cacheSize: 500,
        logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
        secretsManagerTtl: Duration.seconds(300),
      }
    );

    const logGroup = new logs.LogGroup(this, `${functionName}-log-group`, {
      logGroupName: `/aws-lambda/${functionName}`,
      retention: props.logging.retentionDays || 7,
      removalPolicy: RemovalPolicy.DESTROY,
      logGroupClass: logs.LogGroupClass.INFREQUENT_ACCESS,
    });

    const dynatraceSecret = secretsmanager.Secret.fromSecretNameV2(
      this,
      "DynatraceSecret",
      props.dynatraceCredentialsSecretName
    );

    this.function = new lambdaNodejs.NodejsFunction(
      scope,
      functionName,
      {
        runtime: lambda.Runtime.NODEJS_20_X,
        entry: props.handlerFilePath,
        handler: props.handlerFunctionName,
        description: props.description,
        functionName: `${functionName}`,
        memorySize: props.memorySize || 1024,
        timeout: Duration.seconds(props.timeout|| 900),
        vpc: props.vpc,
        securityGroups: [props.securityGroup],
        logGroup,
        loggingFormat: lambda.LoggingFormat.JSON,
        bundling: {
          externalModules: [],
        },
        paramsAndSecrets,
        environment: {
          AWS_LAMBDA_EXEC_WRAPPER: dynatraceSecret
            .secretValueFromJson("AWS_LAMBDA_EXEC_WRAPPER")
            .unsafeUnwrap(),
          DT_TENANT: dynatraceSecret
            .secretValueFromJson("DT_TENANT")
            .unsafeUnwrap(),
          DT_CLUSTER_ID: dynatraceSecret
            .secretValueFromJson("DT_CLUSTER_ID")
            .unsafeUnwrap(),
          DT_CONNECTION_BASE_URL: dynatraceSecret
            .secretValueFromJson("DT_CONNECTION_BASE_URL")
            .unsafeUnwrap(),
          DT_CONNECTION_AUTH_TOKEN: dynatraceSecret
            .secretValueFromJson("DT_CONNECTION_AUTH_TOKEN")
            .unsafeUnwrap(),
        ...props.envVariables
        },
        layers: [
          lambda.LayerVersion.fromLayerVersionArn(
            this,
            "DynatraceOneAgentLayer",
            `arn:aws:lambda:${props.region}:725887861453:layer:Dynatrace_OneAgent_1_295_3_20240729-145043_nodejs:1`
          )
        ],
      }
    );
  }
}
