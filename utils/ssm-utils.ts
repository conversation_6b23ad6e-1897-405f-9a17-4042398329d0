import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";

const ssm = new SSMClient({});

/**
 * Fetches a parameter value from AWS SSM Parameter Store.
 * Throws an error if the parameter is missing or empty.
 */
export async function getSsmValue(name: string): Promise<string> {
  const command = new GetParameterCommand({ Name: name });
  const response = await ssm.send(command);
  console.log('-------response------->',response)

  if (!response.Parameter?.Value) {
    throw new Error(`Missing SSM parameter: ${name}`);
  }

  return response.Parameter.Value;
}