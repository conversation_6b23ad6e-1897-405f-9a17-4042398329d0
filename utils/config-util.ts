import {
  AppConfig,
  CommonResourceStackConfig,
  DefaultStackSynthesizer,
} from "../configs/config.interface";

const getConfig = (env: string): AppConfig => {
  const validEnvs = ["intca1", "batca1", "preprodca1", "prodca1"];
  if (!validEnvs.includes(env)) {
    throw new Error(
      `Invalid environment: ${env}. Must be one of ${validEnvs.join(", ")}`
    );
  }

  try {
    const config = require(`../configs/${env}.json`);
    return config;
  } catch (error) {
    throw new Error(`Configuration file for environment ${env}.json not found`);
  }
};

export {
  getConfig,
  AppConfig,
  CommonResourceStackConfig,
  DefaultStackSynthesizer,
};
