import * as lambda from "aws-cdk-lib/aws-lambda";
import * as lambdaNodejs from "aws-cdk-lib/aws-lambda-nodejs";
import * as secretsmanager from "aws-cdk-lib/aws-secretsmanager";
import * as logs from "aws-cdk-lib/aws-logs";

import { Duration, RemovalPolicy, StackProps } from "aws-cdk-lib";
import { Construct } from "constructs";
import { ISecurityGroup, IVpc, Subnet } from "aws-cdk-lib/aws-ec2";
import path from "path";

import {
  AppConfig,
  CommonResourceStackConfig,
} from "../../../configs/config.interface";

export interface S3UploaderLambdaStackProps extends StackProps {
  config: AppConfig;
  stackConfigProps: CommonResourceStackConfig;
  vpc: IVpc;
  securityGroup: ISecurityGroup;
}

export class S3UploaderLambdaStack extends Construct {
  public readonly function: lambdaNodejs.NodejsFunction;

  constructor(scope: Construct, id: string, props: S3UploaderLambdaStackProps) {
    super(scope, id);

    const envName = props.config.environment;
    const functionName = props.stackConfigProps.resources.s3UploaderLambda.functionName + '-' + envName;

    const paramsAndSecrets = lambda.ParamsAndSecretsLayerVersion.fromVersion(
      lambda.ParamsAndSecretsVersions.V1_0_103,
      {
        cacheSize: 500,
        logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
        secretsManagerTtl: Duration.seconds(300),
      }
    );

    const logGroup = new logs.LogGroup(this, "S3UploaderLambdaLogGroup", {
      logGroupName: `/aws-lambda/${functionName}`,
      retention: props.config.lambda.logging.retentionDays,
      removalPolicy: RemovalPolicy.DESTROY,
      logGroupClass: logs.LogGroupClass.INFREQUENT_ACCESS,
    });

    const dynatraceSecret = secretsmanager.Secret.fromSecretNameV2(
      this,
      "DynatraceSecret",
      props.config.secretsManager.dynatraceCredentialsSecretName
    );

    this.function = new lambdaNodejs.NodejsFunction(
      scope,
      functionName,
      {
        runtime: lambda.Runtime.NODEJS_20_X,
        entry: path.join(__dirname, "../src/index.ts"),
        handler: "s3UploaderHandler",
        description: "Handle Refresh Token Lambda",
        functionName: `${functionName}`,
        memorySize: props.config.lambda.memorySize,
        timeout: Duration.seconds(props.config.lambda.timeout),
        vpc: props.vpc,
        securityGroups: [props.securityGroup],
        logGroup,
        loggingFormat: lambda.LoggingFormat.JSON,
        bundling: {
          externalModules: [],
        },
        paramsAndSecrets,
        environment: {
          AWS_LAMBDA_EXEC_WRAPPER: dynatraceSecret
            .secretValueFromJson("AWS_LAMBDA_EXEC_WRAPPER")
            .unsafeUnwrap(),
          DT_TENANT: dynatraceSecret
            .secretValueFromJson("DT_TENANT")
            .unsafeUnwrap(),
          DT_CLUSTER_ID: dynatraceSecret
            .secretValueFromJson("DT_CLUSTER_ID")
            .unsafeUnwrap(),
          DT_CONNECTION_BASE_URL: dynatraceSecret
            .secretValueFromJson("DT_CONNECTION_BASE_URL")
            .unsafeUnwrap(),
          DT_CONNECTION_AUTH_TOKEN: dynatraceSecret
            .secretValueFromJson("DT_CONNECTION_AUTH_TOKEN")
            .unsafeUnwrap(),
        },
        layers: [
          lambda.LayerVersion.fromLayerVersionArn(
            this,
            "DynatraceOneAgentLayer",
            `arn:aws:lambda:${props.config.region}:725887861453:layer:Dynatrace_OneAgent_1_295_3_20240729-145043_nodejs:1`
          ),
        ],
      }
    );
  }
}
