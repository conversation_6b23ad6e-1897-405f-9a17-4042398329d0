import * as cdk from "aws-cdk-lib";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { Construct } from "constructs";
import {
  AppConfig,
  DefaultStackSynthesizer,
  CommonResourceStackConfig,
} from "../../utils/config-util";

import { S3UploaderLambdaStack } from "./resources/s3-uploader-lambda";

export interface CommonResourceStackProps extends cdk.StackProps {
  config: AppConfig;
  synthesizer: DefaultStackSynthesizer;
  stackConfigProps: CommonResourceStackConfig;
}
export class CommonResourceStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: CommonResourceStackProps) {
    super(scope, id, {
      env: {
        account: props.config.account,
        region: props.config.region,
      },
      stackName: props.stackConfigProps.stackName,
      ...props,
      synthesizer: props.synthesizer,
    });

    // Instantiate resources
    const vpc = ec2.Vpc.fromLookup(this, "VPC", {
      vpcId: props.config.vpc.vpcId,
      isDefault: false,
    });

    const securityGroupId = cdk.Fn.importValue("sgIdDigitalOdsRdsCredentials");
    const securityGroup = ec2.SecurityGroup.fromSecurityGroupId(
      this,
      "sgIdDigitalOdsRdsCredentials",
      securityGroupId
    );
    console.log('---securityGroup----->',securityGroup)

    const s3UploaderLambda = new S3UploaderLambdaStack(
      this,
      "ac-odh-webfocus-common-resources-uploader",
      {
        vpc,
        securityGroup,
        config: props.config,
        stackConfigProps: props.stackConfigProps,
      }
    );
  }
}
