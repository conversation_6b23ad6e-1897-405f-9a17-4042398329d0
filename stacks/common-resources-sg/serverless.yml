service: ac-odh-common-resources-sg

provider:
  name: aws
  runtime: nodejs20.x
  region: ${opt:region, "ca-central-1"}
  stage: ${opt:stage, "intca1"}
  stackTags: ${self:custom.tags}
  deploymentBucket:
    name: ${cf:ac-reusable-deployment-bucket-${self:provider.stage}.ACDigitalBucketName}

custom:
  defaults: ${file(../../defaults.yml)}
  tags: ${self:custom.defaults.custom.tags}
  service: ${self:custom.tags.service}
  base: ${self:service}-${self:provider.stage}
  params: ${self:custom.defaults.custom.params.${self:provider.stage}}

resources:
  Outputs:
    sgIdDigitalOdsRdsCredentials:
      Description: Security Group ID for Digital ODS RDS access
      Value: ${self:custom.params.SECURITY_GROUP_ID}
      Export:
        Name: ${self:custom.base}.sgIdDigitalOdsRdsCredentials
