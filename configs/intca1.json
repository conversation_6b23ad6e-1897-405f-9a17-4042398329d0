{"account": "************", "region": "ca-central-1", "environment": "intca1", "tags": {"tower": "operations", "department-id": "1904", "department-name": "ops-digital", "CostCode": "1904", "ProjectName": "ODH", "SharedResource": "Yes", "Application": "PNR Store - ODH Shared Resources", "TechOwner": "<PERSON><PERSON><PERSON>", "BusinessOwner": "<PERSON><PERSON><PERSON>", "Criticality": "Critical", "Sensitivity": "High", "RecoveryTimeObjective": "< 15 mins", "RecoveryPointObjective": "< 15 mins", "Type": "Customer", "BusinessImpact": "High", "ComplianceRequirement": "NA", "Observability": "Yes", "Environment": "INT"}, "vpc": {"vpcId": "vpc-0b8bcf38152af77cc", "securityGroupId": "sg-07612cb342a5be91d"}, "secretsManager": {"traxDbSecret": "intca1/trax-external-db/db-credentials", "dynatraceCredentialsSecretName": "/int/dbaas/dynatrace/oneagent_credentials"}, "lambda": {"timeout": 300, "memorySize": 1024, "logging": {"retentionDays": 14, "logLevel": "debug"}}, "defaultStackSynthesizer": {"cdkBootstrapConfigSSM": "/cdk-bootstrap/hnb659fds/version", "bucketPrefix": "ac-odh-batch-distribution-intca1/ac-odh-batch-distribution-webfocus/intca1/"}, "stacks": {"CommonResourceStack": {"stackName": "ac-odh-webfocus-common-resources", "resources": {"s3UploaderLambda": {"functionName": "ac-odh-webfocus-common-resources-uploader"}}}}}