import { DefaultStackSynthesizer } from "aws-cdk-lib";

export interface CommonResourceStackConfig {
  stackName: string;
  resources: {
    s3UploaderLambda: {
      functionName: string;
    };
  };
}

export interface AppConfig {
  account: string;
  region: string;
  environment: string;
  tags: {
    tower: string;
    "department-id": string;
    "department-name": string;
    CostCode: string;
    ProjectName: string;
    SharedResource: string;
    Application: string;
    TechOwner: string;
    BusinessOwner: string;
    Criticality: string;
    Sensitivity: string;
    RecoveryTimeObjective: string;
    RecoveryPointObjective: string;
    Type: string;
    BusinessImpact: string;
    ComplianceRequirement: string;
    Observability: string;
    Environment: string;
  };
  vpc: {
    vpcId: string;
    securityGroupId: string;
  };
  secretsManager: {
    traxDbSecret: string;
    dynatraceCredentialsSecretName: string;
  };
  lambda: {
    timeout: number;
    memorySize: number;
    logging: {
      retentionDays: number;
    };
  };
  defaultStackSynthesizer: {
    cdkBootstrapConfigSSM: string;
    bucketPrefix: string;
  };
  stacks: {
    CommonResourceStack: CommonResourceStackConfig;
  };
}

export { DefaultStackSynthesizer };
